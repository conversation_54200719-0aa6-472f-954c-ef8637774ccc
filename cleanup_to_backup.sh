#!/bin/bash

# 代码清理脚本 - 将可删除文件移动到backup文件夹
# 保持原有目录结构，便于恢复

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否在正确的目录
if [ ! -f "package.json" ] || [ ! -d "src" ]; then
    print_error "请在项目根目录运行此脚本"
    exit 1
fi

# 创建backup目录结构
print_info "创建backup目录结构..."
mkdir -p backup/{public,scripts,src/store,deploy,server,routes}

# 移动函数
move_file() {
    local src_file="$1"
    local dest_dir="$2"
    
    if [ -f "$src_file" ]; then
        mkdir -p "backup/$dest_dir"
        mv "$src_file" "backup/$dest_dir/"
        print_success "已移动: $src_file -> backup/$dest_dir/"
    elif [ -d "$src_file" ]; then
        mkdir -p "backup/$dest_dir"
        mv "$src_file" "backup/$dest_dir/"
        print_success "已移动目录: $src_file -> backup/$dest_dir/"
    else
        print_warning "文件不存在，跳过: $src_file"
    fi
}

print_info "开始清理代码库..."

# 1. 重复的配置文件
print_info "1. 清理重复的配置文件..."
move_file "server-package.json" "."

# 2. 测试和临时文件
print_info "2. 清理测试和临时文件..."
move_file "api-interceptor-test.html" "."
move_file "public/test-api-interceptor.html" "public"
move_file "test-api-failure.html" "."
move_file "simulate-server-down.html" "."

# 3. 临时文件
print_info "3. 清理临时文件..."
move_file "~" "."

# 4. 空目录
print_info "4. 清理空目录..."
if [ -d "src/store/{slices}" ]; then
    move_file "src/store/{slices}" "src/store"
fi

# 5. 重复的routes目录（如果存在于根目录）
print_info "5. 检查重复的routes目录..."
if [ -d "routes" ] && [ -d "server/src/routes" ]; then
    print_warning "发现重复的routes目录，移动根目录下的routes到backup"
    move_file "routes" "."
fi

print_success "代码清理完成！"
print_info "所有文件已移动到backup文件夹，保持原有目录结构"
print_info "如需恢复文件，可以从backup文件夹复制回原位置"

# 显示backup目录结构
print_info "Backup目录结构："
if command -v tree >/dev/null 2>&1; then
    tree backup/ || ls -la backup/
else
    find backup/ -type f | head -20
    echo "..."
fi

print_info "清理统计："
echo "- 已移动的文件数量: $(find backup/ -type f 2>/dev/null | wc -l)"
echo "- 已移动的目录数量: $(find backup/ -type d 2>/dev/null | wc -l)"
echo "- Backup文件夹大小: $(du -sh backup/ 2>/dev/null | cut -f1)"
