<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API拦截器测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 700px;
            width: 90%;
        }
        
        .title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        .subtitle {
            font-size: 16px;
            color: #7f8c8d;
            margin-bottom: 30px;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            min-width: 180px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .message {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            font-weight: 500;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .message.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            text-align: left;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🔧 API拦截器测试</h1>
        <p class="subtitle">测试API失败时自动打开控制台页面</p>
        
        <div class="actions">
            <button id="testServerDownBtn" class="btn" onclick="testServerDownAPI()">
                测试服务器停止API
            </button>
            <button id="testLocalBtn" class="btn" onclick="testLocalAPI()">
                测试本地API
            </button>
            <button id="testProductionBtn" class="btn" onclick="testProductionAPI()">
                测试生产环境API
            </button>
        </div>
        
        <div id="message" class="message" style="display: none;"></div>
        
        <div class="code-block">
            <strong>测试说明：</strong><br>
            1. 点击"测试服务器停止API"模拟服务器停止<br>
            2. 应该会自动打开服务器控制台页面<br>
            3. 检查浏览器控制台查看详细日志<br><br>
            
            <strong>当前状态：</strong><br>
            - 本地开发服务器: 🟢 运行中 (http://localhost:5173)<br>
            - 生产服务器: 🔴 离线 (https://pv-analysis.top)<br>
            - 控制台页面: https://pv-analysis.top/restart.html
        </div>
    </div>

    <script>
        // 服务器控制台页面URL
        const SERVER_CONSOLE_URL = 'https://pv-analysis.top/restart.html';
        
        // 检查是否已经打开了控制台页面
        let consoleWindowOpened = false;
        
        // 打开服务器控制台页面
        const openServerConsole = () => {
            if (!consoleWindowOpened) {
                consoleWindowOpened = true;
                console.log('🚀 正在打开服务器控制台页面...');
                
                const consoleWindow = window.open(SERVER_CONSOLE_URL, 'serverConsole', 'width=600,height=700,scrollbars=yes,resizable=yes');
                
                // 监听窗口关闭事件，重置标志
                if (consoleWindow) {
                    const checkClosed = setInterval(() => {
                        if (consoleWindow.closed) {
                            consoleWindowOpened = false;
                            clearInterval(checkClosed);
                            console.log('📱 服务器控制台页面已关闭');
                        }
                    }, 1000);
                    
                    showMessage('✅ 已自动打开服务器控制台页面！', 'info');
                } else {
                    showMessage('❌ 无法打开控制台页面，请检查弹窗设置', 'error');
                }
                
                // 5分钟后重置标志
                setTimeout(() => {
                    consoleWindowOpened = false;
                }, 5 * 60 * 1000);
            } else {
                console.log('📱 服务器控制台页面已经打开');
                showMessage('ℹ️ 服务器控制台页面已经打开', 'info');
            }
        };
        
        // 检查是否为服务器连接错误
        const isServerConnectionError = (error) => {
            console.log('🔍 检查错误类型:', error);
            
            // 网络错误或服务器无响应
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                console.log('✅ 检测到网络连接错误');
                return true;
            }
            
            // 请求超时
            if (error.name === 'AbortError') {
                console.log('✅ 检测到请求超时');
                return true;
            }
            
            // HTTP错误状态码
            if (error.message.includes('HTTP 5')) {
                console.log('✅ 检测到服务器错误 (5xx)');
                return true;
            }
            
            // 503 Service Unavailable
            if (error.message.includes('503')) {
                console.log('✅ 检测到503服务不可用错误');
                return true;
            }
            
            return false;
        };
        
        // 显示消息
        function showMessage(text, type) {
            const message = document.getElementById('message');
            message.textContent = text;
            message.className = `message ${type}`;
            message.style.display = 'block';
            
            // 10秒后自动隐藏
            setTimeout(() => {
                message.style.display = 'none';
            }, 10000);
        }
        
        // 通用API测试函数
        async function testAPI(url, buttonId, apiName) {
            const btn = document.getElementById(buttonId);
            btn.innerHTML = `<span class="loading"></span>测试中...`;
            btn.disabled = true;
            
            try {
                console.log(`🔍 测试${apiName}: ${url}`);
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                console.log(`📡 响应状态: ${response.status} ${response.statusText}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log(`✅ ${apiName}调用成功:`, data);
                showMessage(`${apiName}调用成功`, 'success');
            } catch (error) {
                console.error(`❌ ${apiName}调用失败:`, error);
                
                // 检查是否为服务器连接错误
                if (isServerConnectionError(error)) {
                    console.warn(`🚨 检测到服务器连接错误，正在打开服务器控制台页面...`);
                    openServerConsole();
                } else {
                    showMessage(`${apiName}失败: ${error.message}`, 'error');
                }
            }
            
            btn.innerHTML = `测试${apiName}`;
            btn.disabled = false;
        }
        
        // 测试服务器停止API（应该失败并打开控制台）
        async function testServerDownAPI() {
            await testAPI('https://pv-analysis.top/api/health', 'testServerDownBtn', '服务器停止API');
        }
        
        // 测试本地API（应该成功）
        async function testLocalAPI() {
            await testAPI('http://localhost:5173/api/health', 'testLocalBtn', '本地API');
        }
        
        // 测试生产环境API（应该失败）
        async function testProductionAPI() {
            await testAPI('https://pv-analysis.top/api/projects', 'testProductionBtn', '生产环境API');
        }
    </script>
</body>
</html>
