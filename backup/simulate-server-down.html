<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模拟服务器停机测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 700px;
            width: 90%;
        }
        
        .title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        .subtitle {
            font-size: 16px;
            color: #7f8c8d;
            margin-bottom: 30px;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            min-width: 180px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .message {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            font-weight: 500;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .message.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .message.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            text-align: left;
            overflow-x: auto;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online {
            background: #28a745;
        }
        
        .status-offline {
            background: #dc3545;
        }
        
        .status-unknown {
            background: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🔧 服务器停机模拟测试</h1>
        <p class="subtitle">测试当服务器不可用时自动打开控制台页面的功能</p>
        
        <div id="serverStatus" class="message info">
            <span class="status-indicator status-unknown"></span>
            正在检查服务器状态...
        </div>
        
        <div class="actions">
            <button id="checkBtn" class="btn btn-success" onclick="checkServerStatus()">
                检查服务器状态
            </button>
            <button id="testBtn" class="btn" onclick="testAPICall()">
                测试API调用
            </button>
            <button id="stopBtn" class="btn btn-danger" onclick="stopServer()">
                停止服务器
            </button>
        </div>
        
        <div id="message" class="message" style="display: none;"></div>
        
        <div class="code-block">
            <strong>测试步骤：</strong><br>
            1. 首先点击"检查服务器状态"确认服务器在线<br>
            2. 点击"停止服务器"模拟服务器停机<br>
            3. 再次点击"测试API调用"，应该会自动打开控制台页面<br>
            4. 在控制台页面中可以重启服务器<br><br>
            
            <strong>注意：</strong><br>
            - 检查浏览器控制台查看详细日志<br>
            - 控制台页面会在新窗口中打开<br>
            - 如果弹窗被阻止，请允许弹窗
        </div>
    </div>

    <script>
        // 服务器控制台页面URL
        const SERVER_CONSOLE_URL = 'https://pv-analysis.top/restart.html';
        
        // 检查是否已经打开了控制台页面
        let consoleWindowOpened = false;
        
        // 打开服务器控制台页面
        const openServerConsole = () => {
            if (!consoleWindowOpened) {
                consoleWindowOpened = true;
                console.log('🚀 正在打开服务器控制台页面...');
                
                const consoleWindow = window.open(SERVER_CONSOLE_URL, 'serverConsole', 'width=600,height=700,scrollbars=yes,resizable=yes');
                
                // 监听窗口关闭事件，重置标志
                if (consoleWindow) {
                    const checkClosed = setInterval(() => {
                        if (consoleWindow.closed) {
                            consoleWindowOpened = false;
                            clearInterval(checkClosed);
                            console.log('📱 服务器控制台页面已关闭');
                        }
                    }, 1000);
                    
                    showMessage('已打开服务器控制台页面，请在新窗口中操作', 'info');
                } else {
                    showMessage('无法打开控制台页面，请检查弹窗设置', 'warning');
                }
                
                // 5分钟后重置标志，避免长时间无法再次打开
                setTimeout(() => {
                    consoleWindowOpened = false;
                }, 5 * 60 * 1000);
            } else {
                console.log('📱 服务器控制台页面已经打开');
                showMessage('服务器控制台页面已经打开', 'info');
            }
        };
        
        // 检查是否为服务器连接错误
        const isServerConnectionError = (error) => {
            // 网络错误或服务器无响应
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                return true;
            }
            
            // 请求超时
            if (error.name === 'AbortError') {
                return true;
            }
            
            return false;
        };
        
        // 显示消息
        function showMessage(text, type) {
            const message = document.getElementById('message');
            message.textContent = text;
            message.className = `message ${type}`;
            message.style.display = 'block';
            
            // 10秒后自动隐藏
            setTimeout(() => {
                message.style.display = 'none';
            }, 10000);
        }
        
        // 更新服务器状态显示
        function updateServerStatus(online, message) {
            const statusDiv = document.getElementById('serverStatus');
            const indicator = statusDiv.querySelector('.status-indicator');
            
            if (online) {
                indicator.className = 'status-indicator status-online';
                statusDiv.className = 'message success';
                statusDiv.innerHTML = '<span class="status-indicator status-online"></span>' + message;
            } else {
                indicator.className = 'status-indicator status-offline';
                statusDiv.className = 'message error';
                statusDiv.innerHTML = '<span class="status-indicator status-offline"></span>' + message;
            }
        }
        
        // 检查服务器状态
        async function checkServerStatus() {
            const btn = document.getElementById('checkBtn');
            btn.innerHTML = '<span class="loading"></span>检查中...';
            btn.disabled = true;
            
            try {
                console.log('🔍 检查服务器状态...');
                
                const response = await fetch('https://pv-analysis.top/api/health', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('✅ 服务器在线:', data);
                updateServerStatus(true, `服务器在线 - 运行时间: ${Math.floor(data.uptime)}秒`);
                showMessage('服务器状态检查成功', 'success');
            } catch (error) {
                console.error('❌ 服务器状态检查失败:', error);
                updateServerStatus(false, '服务器离线或不可访问');
                showMessage(`服务器状态检查失败: ${error.message}`, 'error');
            }
            
            btn.innerHTML = '检查服务器状态';
            btn.disabled = false;
        }
        
        // 测试API调用
        async function testAPICall() {
            const btn = document.getElementById('testBtn');
            btn.innerHTML = '<span class="loading"></span>测试中...';
            btn.disabled = true;
            
            try {
                console.log('🔍 测试API调用...');
                
                const response = await fetch('https://pv-analysis.top/api/projects', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('✅ API调用成功:', data);
                showMessage('API调用成功', 'success');
                updateServerStatus(true, '服务器在线 - API正常工作');
            } catch (error) {
                console.error('❌ API调用失败:', error);
                
                // 检查是否为服务器连接错误
                if (isServerConnectionError(error)) {
                    console.warn('🚨 检测到服务器连接错误，正在打开服务器控制台页面...');
                    updateServerStatus(false, '服务器连接失败 - 已打开控制台页面');
                    openServerConsole();
                } else {
                    updateServerStatus(false, '服务器API错误');
                    showMessage(`API调用失败: ${error.message}`, 'error');
                }
            }
            
            btn.innerHTML = '测试API调用';
            btn.disabled = false;
        }
        
        // 停止服务器
        async function stopServer() {
            if (!confirm('确定要停止服务器吗？这将模拟服务器停机状态。')) {
                return;
            }
            
            const btn = document.getElementById('stopBtn');
            btn.innerHTML = '<span class="loading"></span>停止中...';
            btn.disabled = true;
            
            try {
                console.log('🛑 正在停止服务器...');
                
                const response = await fetch('https://pv-analysis.top/api/system/restart', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    console.log('✅ 停止命令发送成功:', data);
                    showMessage('服务器停止命令已发送，请等待几秒后测试API调用', 'info');
                    updateServerStatus(false, '服务器正在停止...');
                    
                    // 5秒后自动检查状态
                    setTimeout(() => {
                        checkServerStatus();
                    }, 5000);
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                console.error('❌ 停止服务器失败:', error);
                showMessage(`停止服务器失败: ${error.message}`, 'error');
            }
            
            btn.innerHTML = '停止服务器';
            btn.disabled = false;
        }
        
        // 页面加载时自动检查服务器状态
        window.onload = function() {
            checkServerStatus();
        };
    </script>
</body>
</html>
