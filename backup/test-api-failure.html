<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API失败测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 600px;
            width: 90%;
        }
        
        .title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        .subtitle {
            font-size: 16px;
            color: #7f8c8d;
            margin-bottom: 30px;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            min-width: 150px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .message {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            font-weight: 500;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .message.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            text-align: left;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🧪 API失败自动重启测试</h1>
        <p class="subtitle">测试当API不可用时自动打开服务器控制台页面</p>
        
        <div class="actions">
            <button id="testBtn" class="btn" onclick="testAPIFailure()">
                测试API调用
            </button>
            <button id="testHealthBtn" class="btn" onclick="testHealthAPI()">
                测试健康检查
            </button>
            <button id="testProjectBtn" class="btn" onclick="testProjectAPI()">
                测试项目API
            </button>
        </div>
        
        <div id="message" class="message" style="display: none;"></div>
        
        <div class="code-block">
            <strong>测试说明：</strong><br>
            1. 点击上面的按钮测试不同的API调用<br>
            2. 当API不可用时，会自动打开服务器控制台页面<br>
            3. 控制台页面允许用户重启服务器<br>
            4. 检查浏览器控制台查看详细日志
        </div>
    </div>

    <script>
        // 服务器控制台页面URL
        const SERVER_CONSOLE_URL = '/restart.html';
        
        // 检查是否已经打开了控制台页面
        let consoleWindowOpened = false;
        
        // 打开服务器控制台页面
        const openServerConsole = () => {
            if (!consoleWindowOpened) {
                consoleWindowOpened = true;
                console.log('🚀 正在打开服务器控制台页面...');
                
                const consoleWindow = window.open(SERVER_CONSOLE_URL, 'serverConsole', 'width=600,height=700,scrollbars=yes,resizable=yes');
                
                // 监听窗口关闭事件，重置标志
                if (consoleWindow) {
                    const checkClosed = setInterval(() => {
                        if (consoleWindow.closed) {
                            consoleWindowOpened = false;
                            clearInterval(checkClosed);
                            console.log('📱 服务器控制台页面已关闭');
                        }
                    }, 1000);
                }
                
                // 5分钟后重置标志，避免长时间无法再次打开
                setTimeout(() => {
                    consoleWindowOpened = false;
                }, 5 * 60 * 1000);
            } else {
                console.log('📱 服务器控制台页面已经打开');
            }
        };
        
        // 检查是否为服务器连接错误
        const isServerConnectionError = (error) => {
            // 网络错误或服务器无响应
            if (error.code === 'ECONNREFUSED' || error.code === 'NETWORK_ERROR') {
                return true;
            }
            
            // 请求超时
            if (error.code === 'ECONNABORTED' && error.message.includes('timeout')) {
                return true;
            }
            
            // 没有收到响应（服务器可能已停止）
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                return true;
            }
            
            return false;
        };
        
        // 显示消息
        function showMessage(text, type) {
            const message = document.getElementById('message');
            message.textContent = text;
            message.className = `message ${type}`;
            message.style.display = 'block';
            
            // 5秒后自动隐藏
            setTimeout(() => {
                message.style.display = 'none';
            }, 5000);
        }
        
        // 测试API失败
        async function testAPIFailure() {
            const btn = document.getElementById('testBtn');
            btn.innerHTML = '<span class="loading"></span>测试中...';
            btn.disabled = true;
            
            try {
                console.log('🔍 测试API调用...');
                
                // 尝试调用一个不存在的API端点
                const response = await fetch('/api/test-nonexistent-endpoint', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                showMessage('API调用成功（意外）', 'success');
            } catch (error) {
                console.error('❌ API调用失败:', error);
                
                // 检查是否为服务器连接错误
                if (isServerConnectionError(error)) {
                    console.warn('🚨 检测到服务器连接错误，正在打开服务器控制台页面...');
                    openServerConsole();
                    showMessage('检测到服务器连接错误，已打开控制台页面', 'info');
                } else {
                    showMessage(`API调用失败: ${error.message}`, 'error');
                }
            }
            
            btn.innerHTML = '测试API调用';
            btn.disabled = false;
        }
        
        // 测试健康检查API
        async function testHealthAPI() {
            const btn = document.getElementById('testHealthBtn');
            btn.innerHTML = '<span class="loading"></span>测试中...';
            btn.disabled = true;
            
            try {
                console.log('🔍 测试健康检查API...');
                
                const response = await fetch('/api/health', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('✅ 健康检查成功:', data);
                showMessage('健康检查API调用成功', 'success');
            } catch (error) {
                console.error('❌ 健康检查失败:', error);
                
                // 检查是否为服务器连接错误
                if (isServerConnectionError(error)) {
                    console.warn('🚨 健康检查失败，正在打开服务器控制台页面...');
                    openServerConsole();
                    showMessage('健康检查失败，已打开控制台页面', 'info');
                } else {
                    showMessage(`健康检查失败: ${error.message}`, 'error');
                }
            }
            
            btn.innerHTML = '测试健康检查';
            btn.disabled = false;
        }
        
        // 测试项目API
        async function testProjectAPI() {
            const btn = document.getElementById('testProjectBtn');
            btn.innerHTML = '<span class="loading"></span>测试中...';
            btn.disabled = true;
            
            try {
                console.log('🔍 测试项目API...');
                
                const response = await fetch('/api/projects', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('✅ 项目API调用成功:', data);
                showMessage('项目API调用成功', 'success');
            } catch (error) {
                console.error('❌ 项目API调用失败:', error);
                
                // 检查是否为服务器连接错误
                if (isServerConnectionError(error)) {
                    console.warn('🚨 项目API失败，正在打开服务器控制台页面...');
                    openServerConsole();
                    showMessage('项目API失败，已打开控制台页面', 'info');
                } else {
                    showMessage(`项目API失败: ${error.message}`, 'error');
                }
            }
            
            btn.innerHTML = '测试项目API';
            btn.disabled = false;
        }
    </script>
</body>
</html>
