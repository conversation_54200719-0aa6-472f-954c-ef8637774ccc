/**
 * 设备路由
 */
const express = require('express');
const router = express.Router();
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// 数据存储路径
const dataDir = path.join(__dirname, '../../data/equipment');

// 确保数据目录存在
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

// 获取所有设备列表
router.get('/', (req, res) => {
  try {
    if (!fs.existsSync(dataDir)) {
      return res.json({ success: true, data: [] });
    }

    const files = fs.readdirSync(dataDir);
    const dataList = [];

    files.forEach(file => {
      if (file.endsWith('.json')) {
        try {
          const filePath = path.join(dataDir, file);
          const fileData = fs.readFileSync(filePath, 'utf8');
          const data = JSON.parse(fileData);
          dataList.push(data);
        } catch (err) {
          console.error(`读取文件 ${file} 时出错:`, err);
        }
      }
    });

    // 按更新时间排序，最新的在前面
    dataList.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));

    // 根据查询参数过滤设备类型
    const { type } = req.query;
    if (type) {
      const filteredList = dataList.filter(item => item.type === type);
      return res.json({ success: true, data: filteredList });
    }

    res.json({ success: true, data: dataList });
  } catch (err) {
    console.error('获取设备列表时出错:', err);
    res.status(500).json({ success: false, message: err.message });
  }
});

// 获取单个设备
router.get('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const filePath = path.join(dataDir, `${id}.json`);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ success: false, message: '找不到指定的设备' });
    }

    const fileData = fs.readFileSync(filePath, 'utf8');
    const data = JSON.parse(fileData);

    res.json({ success: true, data });
  } catch (err) {
    console.error('获取设备时出错:', err);
    res.status(500).json({ success: false, message: err.message });
  }
});

// 创建新的设备
router.post('/', (req, res) => {
  try {
    const { 
      name, 
      type, 
      model, 
      manufacturer, 
      supplier, 
      price, 
      specifications, 
      description,
      attachments 
    } = req.body;

    if (!name || !type) {
      return res.status(400).json({ success: false, message: '缺少必要的数据字段' });
    }

    const id = uuidv4();
    const now = new Date().toISOString();

    const equipmentData = {
      id,
      name,
      type, // 'pv', 'inverter', 'storage'
      model: model || '',
      manufacturer: manufacturer || '',
      supplier: supplier || '',
      price: price || 0,
      specifications: specifications || {},
      description: description || '',
      attachments: attachments || [],
      createdAt: now,
      updatedAt: now
    };

    const filePath = path.join(dataDir, `${id}.json`);
    fs.writeFileSync(filePath, JSON.stringify(equipmentData, null, 2));

    res.status(201).json({ success: true, data: equipmentData });
  } catch (err) {
    console.error('创建设备时出错:', err);
    res.status(500).json({ success: false, message: err.message });
  }
});

// 更新设备
router.put('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const { 
      name, 
      type, 
      model, 
      manufacturer, 
      supplier, 
      price, 
      specifications, 
      description,
      attachments 
    } = req.body;
    
    const filePath = path.join(dataDir, `${id}.json`);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ success: false, message: '找不到指定的设备' });
    }

    const fileData = fs.readFileSync(filePath, 'utf8');
    const existingData = JSON.parse(fileData);

    const updatedData = {
      ...existingData,
      name: name || existingData.name,
      type: type || existingData.type,
      model: model !== undefined ? model : existingData.model,
      manufacturer: manufacturer !== undefined ? manufacturer : existingData.manufacturer,
      supplier: supplier !== undefined ? supplier : existingData.supplier,
      price: price !== undefined ? price : existingData.price,
      specifications: specifications || existingData.specifications,
      description: description !== undefined ? description : existingData.description,
      attachments: attachments || existingData.attachments,
      updatedAt: new Date().toISOString()
    };

    fs.writeFileSync(filePath, JSON.stringify(updatedData, null, 2));

    res.json({ success: true, data: updatedData });
  } catch (err) {
    console.error('更新设备时出错:', err);
    res.status(500).json({ success: false, message: err.message });
  }
});

// 删除设备
router.delete('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const filePath = path.join(dataDir, `${id}.json`);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ success: false, message: '找不到指定的设备' });
    }

    fs.unlinkSync(filePath);
    res.json({ success: true, message: '设备已成功删除' });
  } catch (err) {
    console.error('删除设备时出错:', err);
    res.status(500).json({ success: false, message: err.message });
  }
});

module.exports = router;
