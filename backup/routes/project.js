/**
 * 项目路由
 */
const express = require('express');
const router = express.Router();
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// 数据存储路径
const dataDir = path.join(__dirname, '../../data/projects');

// 确保数据目录存在
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

// 获取所有项目列表（只返回基本信息，不包含分析结果）
router.get('/', (req, res) => {
  try {
    if (!fs.existsSync(dataDir)) {
      return res.json({ success: true, data: [] });
    }

    const folders = fs.readdirSync(dataDir);
    const projectList = [];

    folders.forEach(folder => {
      const folderPath = path.join(dataDir, folder);
      const indexPath = path.join(folderPath, 'index.json');
      
      if (fs.existsSync(indexPath) && fs.statSync(folderPath).isDirectory()) {
        try {
          const indexData = fs.readFileSync(indexPath, 'utf8');
          const projectInfo = JSON.parse(indexData);
          projectList.push(projectInfo);
        } catch (err) {
          console.error(`读取项目 ${folder} 信息时出错:`, err);
        }
      }
    });

    // 按更新时间排序，最新的在前面
    projectList.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));

    res.json({ success: true, data: projectList });
  } catch (err) {
    console.error('获取项目列表时出错:', err);
    res.status(500).json({ success: false, message: err.message });
  }
});

// 获取单个项目的基本信息
router.get('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const projectDir = path.join(dataDir, id);
    const indexPath = path.join(projectDir, 'index.json');

    if (!fs.existsSync(indexPath)) {
      return res.status(404).json({ success: false, message: '找不到指定的项目' });
    }

    const indexData = fs.readFileSync(indexPath, 'utf8');
    const projectInfo = JSON.parse(indexData);

    res.json({ success: true, data: projectInfo });
  } catch (err) {
    console.error('获取项目信息时出错:', err);
    res.status(500).json({ success: false, message: err.message });
  }
});

// 获取项目的分析结果
router.get('/:id/results', (req, res) => {
  try {
    const { id } = req.params;
    const projectDir = path.join(dataDir, id);
    const resultsPath = path.join(projectDir, 'results.json');

    if (!fs.existsSync(resultsPath)) {
      return res.status(404).json({ success: false, message: '找不到项目的分析结果' });
    }

    const resultsData = fs.readFileSync(resultsPath, 'utf8');
    const results = JSON.parse(resultsData);

    res.json({ success: true, data: results });
  } catch (err) {
    console.error('获取项目分析结果时出错:', err);
    res.status(500).json({ success: false, message: err.message });
  }
});

// 创建新项目
router.post('/', (req, res) => {
  try {
    const { name, location, description, basicInfo } = req.body;

    if (!name) {
      return res.status(400).json({ success: false, message: '缺少必要的数据字段' });
    }

    const id = uuidv4();
    const now = new Date().toISOString();

    // 项目基本信息
    const projectInfo = {
      id,
      name,
      location: location || '',
      description: description || '',
      basicInfo: basicInfo || {},
      status: 'draft', // 'draft', 'analyzed'
      createdAt: now,
      updatedAt: now
    };

    // 创建项目目录
    const projectDir = path.join(dataDir, id);
    if (!fs.existsSync(projectDir)) {
      fs.mkdirSync(projectDir, { recursive: true });
    }

    // 保存项目基本信息
    const indexPath = path.join(projectDir, 'index.json');
    fs.writeFileSync(indexPath, JSON.stringify(projectInfo, null, 2));

    res.status(201).json({ success: true, data: projectInfo });
  } catch (err) {
    console.error('创建项目时出错:', err);
    res.status(500).json({ success: false, message: err.message });
  }
});

// 更新项目基本信息
router.put('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const { name, location, description, basicInfo, status } = req.body;
    
    const projectDir = path.join(dataDir, id);
    const indexPath = path.join(projectDir, 'index.json');

    if (!fs.existsSync(indexPath)) {
      return res.status(404).json({ success: false, message: '找不到指定的项目' });
    }

    const indexData = fs.readFileSync(indexPath, 'utf8');
    const existingInfo = JSON.parse(indexData);

    const updatedInfo = {
      ...existingInfo,
      name: name || existingInfo.name,
      location: location !== undefined ? location : existingInfo.location,
      description: description !== undefined ? description : existingInfo.description,
      basicInfo: basicInfo || existingInfo.basicInfo,
      status: status || existingInfo.status,
      updatedAt: new Date().toISOString()
    };

    fs.writeFileSync(indexPath, JSON.stringify(updatedInfo, null, 2));

    res.json({ success: true, data: updatedInfo });
  } catch (err) {
    console.error('更新项目信息时出错:', err);
    res.status(500).json({ success: false, message: err.message });
  }
});

// 保存项目分析结果
router.post('/:id/results', (req, res) => {
  try {
    const { id } = req.params;
    const { results } = req.body;

    if (!results) {
      return res.status(400).json({ success: false, message: '缺少分析结果数据' });
    }

    const projectDir = path.join(dataDir, id);
    const indexPath = path.join(projectDir, 'index.json');

    if (!fs.existsSync(indexPath)) {
      return res.status(404).json({ success: false, message: '找不到指定的项目' });
    }

    // 更新项目状态为已分析
    const indexData = fs.readFileSync(indexPath, 'utf8');
    const projectInfo = JSON.parse(indexData);
    
    projectInfo.status = 'analyzed';
    projectInfo.updatedAt = new Date().toISOString();
    
    fs.writeFileSync(indexPath, JSON.stringify(projectInfo, null, 2));

    // 保存分析结果
    const resultsPath = path.join(projectDir, 'results.json');
    fs.writeFileSync(resultsPath, JSON.stringify(results, null, 2));

    res.json({ success: true, message: '项目分析结果已保存' });
  } catch (err) {
    console.error('保存项目分析结果时出错:', err);
    res.status(500).json({ success: false, message: err.message });
  }
});

// 删除项目
router.delete('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const projectDir = path.join(dataDir, id);

    if (!fs.existsSync(projectDir)) {
      return res.status(404).json({ success: false, message: '找不到指定的项目' });
    }

    // 递归删除项目目录及其所有内容
    const deleteFolderRecursive = function(folderPath) {
      if (fs.existsSync(folderPath)) {
        fs.readdirSync(folderPath).forEach((file) => {
          const curPath = path.join(folderPath, file);
          if (fs.statSync(curPath).isDirectory()) {
            deleteFolderRecursive(curPath);
          } else {
            fs.unlinkSync(curPath);
          }
        });
        fs.rmdirSync(folderPath);
      }
    };

    deleteFolderRecursive(projectDir);
    res.json({ success: true, message: '项目已成功删除' });
  } catch (err) {
    console.error('删除项目时出错:', err);
    res.status(500).json({ success: false, message: err.message });
  }
});

module.exports = router;
