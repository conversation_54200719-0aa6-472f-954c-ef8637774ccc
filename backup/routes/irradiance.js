/**
 * 辐照度数据路由
 */
const express = require('express');
const router = express.Router();
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// 数据存储路径
const dataDir = path.join(__dirname, '../../data/irradiance');

// 确保数据目录存在
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

// 获取所有辐照度数据列表
router.get('/', (req, res) => {
  try {
    if (!fs.existsSync(dataDir)) {
      return res.json({ success: true, data: [] });
    }

    const files = fs.readdirSync(dataDir);
    const dataList = [];

    files.forEach(file => {
      if (file.endsWith('.json')) {
        try {
          const filePath = path.join(dataDir, file);
          const fileData = fs.readFileSync(filePath, 'utf8');
          const data = JSON.parse(fileData);
          
          // 只返回元数据，不包含完整的8760小时数据
          const { id, name, location, description, createdAt, updatedAt } = data;
          dataList.push({ id, name, location, description, createdAt, updatedAt });
        } catch (err) {
          console.error(`读取文件 ${file} 时出错:`, err);
        }
      }
    });

    // 按更新时间排序，最新的在前面
    dataList.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));

    res.json({ success: true, data: dataList });
  } catch (err) {
    console.error('获取辐照度数据列表时出错:', err);
    res.status(500).json({ success: false, message: err.message });
  }
});

// 获取单个辐照度数据
router.get('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const filePath = path.join(dataDir, `${id}.json`);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ success: false, message: '找不到指定的辐照度数据' });
    }

    const fileData = fs.readFileSync(filePath, 'utf8');
    const data = JSON.parse(fileData);

    res.json({ success: true, data });
  } catch (err) {
    console.error('获取辐照度数据时出错:', err);
    res.status(500).json({ success: false, message: err.message });
  }
});

// 创建新的辐照度数据
router.post('/', (req, res) => {
  try {
    const { name, location, description, hourlyData } = req.body;

    if (!name || !hourlyData || !Array.isArray(hourlyData)) {
      return res.status(400).json({ success: false, message: '缺少必要的数据字段' });
    }

    const id = uuidv4();
    const now = new Date().toISOString();

    const data = {
      id,
      name,
      location: location || '',
      description: description || '',
      createdAt: now,
      updatedAt: now,
      hourlyData
    };

    const filePath = path.join(dataDir, `${id}.json`);
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2));

    // 返回不包含hourlyData的元数据
    const { hourlyData: _, ...metadata } = data;
    res.status(201).json({ success: true, data: metadata });
  } catch (err) {
    console.error('创建辐照度数据时出错:', err);
    res.status(500).json({ success: false, message: err.message });
  }
});

// 更新辐照度数据
router.put('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const { name, location, description, hourlyData } = req.body;
    const filePath = path.join(dataDir, `${id}.json`);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ success: false, message: '找不到指定的辐照度数据' });
    }

    const fileData = fs.readFileSync(filePath, 'utf8');
    const existingData = JSON.parse(fileData);

    const updatedData = {
      ...existingData,
      name: name || existingData.name,
      location: location !== undefined ? location : existingData.location,
      description: description !== undefined ? description : existingData.description,
      hourlyData: hourlyData || existingData.hourlyData,
      updatedAt: new Date().toISOString()
    };

    fs.writeFileSync(filePath, JSON.stringify(updatedData, null, 2));

    // 返回不包含hourlyData的元数据
    const { hourlyData: _, ...metadata } = updatedData;
    res.json({ success: true, data: metadata });
  } catch (err) {
    console.error('更新辐照度数据时出错:', err);
    res.status(500).json({ success: false, message: err.message });
  }
});

// 删除辐照度数据
router.delete('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const filePath = path.join(dataDir, `${id}.json`);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ success: false, message: '找不到指定的辐照度数据' });
    }

    fs.unlinkSync(filePath);
    res.json({ success: true, message: '辐照度数据已成功删除' });
  } catch (err) {
    console.error('删除辐照度数据时出错:', err);
    res.status(500).json({ success: false, message: err.message });
  }
});

module.exports = router;
