/**
 * 电价政策路由
 */
const express = require('express');
const router = express.Router();
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// 数据存储路径
const dataDir = path.join(__dirname, '../../data/electricity-prices');

// 确保数据目录存在
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

// 获取所有电价政策列表
router.get('/', (req, res) => {
  try {
    if (!fs.existsSync(dataDir)) {
      return res.json({ success: true, data: [] });
    }

    const files = fs.readdirSync(dataDir);
    const dataList = [];

    files.forEach(file => {
      if (file.endsWith('.json')) {
        try {
          const filePath = path.join(dataDir, file);
          const fileData = fs.readFileSync(filePath, 'utf8');
          const data = JSON.parse(fileData);
          dataList.push(data);
        } catch (err) {
          console.error(`读取文件 ${file} 时出错:`, err);
        }
      }
    });

    // 按更新时间排序，最新的在前面
    dataList.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));

    res.json({ success: true, data: dataList });
  } catch (err) {
    console.error('获取电价政策列表时出错:', err);
    res.status(500).json({ success: false, message: err.message });
  }
});

// 获取单个电价政策
router.get('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const filePath = path.join(dataDir, `${id}.json`);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ success: false, message: '找不到指定的电价政策' });
    }

    const fileData = fs.readFileSync(filePath, 'utf8');
    const data = JSON.parse(fileData);

    res.json({ success: true, data });
  } catch (err) {
    console.error('获取电价政策时出错:', err);
    res.status(500).json({ success: false, message: err.message });
  }
});

// 创建新的电价政策
router.post('/', (req, res) => {
  try {
    const { name, description, type, data } = req.body;

    if (!name || !type || !data) {
      return res.status(400).json({ success: false, message: '缺少必要的数据字段' });
    }

    const id = uuidv4();
    const now = new Date().toISOString();

    const policyData = {
      id,
      name,
      description: description || '',
      type, // 'constant' 或 'seasonal'
      data, // 对于constant类型，包含单一价格；对于seasonal类型，包含多个季节的价格
      createdAt: now,
      updatedAt: now
    };

    const filePath = path.join(dataDir, `${id}.json`);
    fs.writeFileSync(filePath, JSON.stringify(policyData, null, 2));

    res.status(201).json({ success: true, data: policyData });
  } catch (err) {
    console.error('创建电价政策时出错:', err);
    res.status(500).json({ success: false, message: err.message });
  }
});

// 更新电价政策
router.put('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, type, data } = req.body;
    const filePath = path.join(dataDir, `${id}.json`);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ success: false, message: '找不到指定的电价政策' });
    }

    const fileData = fs.readFileSync(filePath, 'utf8');
    const existingData = JSON.parse(fileData);

    const updatedData = {
      ...existingData,
      name: name || existingData.name,
      description: description !== undefined ? description : existingData.description,
      type: type || existingData.type,
      data: data || existingData.data,
      updatedAt: new Date().toISOString()
    };

    fs.writeFileSync(filePath, JSON.stringify(updatedData, null, 2));

    res.json({ success: true, data: updatedData });
  } catch (err) {
    console.error('更新电价政策时出错:', err);
    res.status(500).json({ success: false, message: err.message });
  }
});

// 删除电价政策
router.delete('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const filePath = path.join(dataDir, `${id}.json`);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ success: false, message: '找不到指定的电价政策' });
    }

    fs.unlinkSync(filePath);
    res.json({ success: true, message: '电价政策已成功删除' });
  } catch (err) {
    console.error('删除电价政策时出错:', err);
    res.status(500).json({ success: false, message: err.message });
  }
});

module.exports = router;
